{% extends 'base.html' %}

{% block title %}النسخ الاحتياطي{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-cloud-upload-alt me-2"></i>النسخ الاحتياطي
        </h1>
        <div class="btn-group" role="group">
            <a href="{% url 'download_backup' %}" class="btn btn-primary">
                <i class="fas fa-download me-1"></i>تحميل محلي
            </a>
            {% if drive_connected %}
                <a href="{% url 'upload_to_drive' %}" class="btn btn-success">
                    <i class="fab fa-google-drive me-1"></i>رفع لـ Google Drive
                </a>
            {% endif %}
        </div>
    </div>

    <!-- حالة Google Drive -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fab fa-google-drive me-2"></i>Google Drive
                    </h6>
                </div>
                <div class="card-body">
                    {% if drive_connected %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>متصل:</strong> {{ drive_message }}
                        </div>
                        <p class="text-muted mb-0">
                            يمكنك رفع النسخ الاحتياطية تلقائياً إلى Google Drive
                        </p>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>غير متصل:</strong> {{ drive_message }}
                        </div>
                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#setupModal">
                            <i class="fas fa-cog me-1"></i>إعداد Google Drive
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">النسخ المحلية</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>تحميل مباشر:</strong> نسخة JSON من جميع البيانات
                    </div>
                    <p class="text-muted mb-3">
                        تشمل: العملاء، الديون، المدفوعات، الفواتير، والإعدادات
                    </p>
                    <a href="{% url 'download_backup' %}" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>تحميل الآن
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة النسخ الاحتياطية من Google Drive -->
    {% if drive_connected and backups %}
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">النسخ الاحتياطية في Google Drive</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>اسم الملف</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الحجم</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for backup in backups %}
                        <tr>
                            <td>
                                <i class="fas fa-file-code text-info me-2"></i>
                                {{ backup.name }}
                            </td>
                            <td>{{ backup.created_time|date:"Y-m-d H:i" }}</td>
                            <td>
                                {% if backup.size %}
                                    {{ backup.size|filesizeformat }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                            <td>
                                {% if backup.link %}
                                    <a href="{{ backup.link }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-external-link-alt me-1"></i>فتح
                                    </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal إعداد Google Drive -->
<div class="modal fade" id="setupModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعداد Google Drive API</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>خطوات الإعداد:</h6>
                <ol>
                    <li class="mb-2">
                        <strong>إنشاء مشروع في Google Cloud Console:</strong>
                        <br><a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a>
                    </li>
                    <li class="mb-2">
                        <strong>تفعيل Google Drive API:</strong>
                        <br>ابحث عن "Google Drive API" وفعله
                    </li>
                    <li class="mb-2">
                        <strong>إنشاء Service Account:</strong>
                        <br>اذهب إلى "IAM & Admin" > "Service Accounts"
                    </li>
                    <li class="mb-2">
                        <strong>تحميل ملف JSON:</strong>
                        <br>احفظ الملف باسم "credentials.json"
                    </li>
                    <li class="mb-2">
                        <strong>إعداد المتغيرات في Render:</strong>
                        <pre class="bg-light p-2 rounded">
GOOGLE_DRIVE_CREDENTIALS = credentials.json
GOOGLE_DRIVE_FOLDER_ID = your_folder_id</pre>
                    </li>
                </ol>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
