<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}محلات أبو علاء - إدارة الديون{% endblock %}</title>

    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        /* العلامة المائية */
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-15deg);
            opacity: 0.15;
            z-index: -1;
            pointer-events: none;
            max-width: 400px;
            max-height: 400px;
            filter: grayscale(20%);
        }

        .watermark img {
            width: 100%;
            height: auto;
            filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
        }

        /* حقوق المبرمج */
        .developer-footer {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 11px;
            z-index: 1000;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .developer-footer:hover {
            background: rgba(102, 126, 234, 1);
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .developer-footer i {
            margin-left: 5px;
        }

        @media print {
            .watermark {
                opacity: 0.08;
                z-index: 1;
            }
            .developer-footer {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .developer-footer {
                bottom: 5px;
                right: 5px;
                padding: 6px 12px;
                font-size: 10px;
            }
        }
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #f8f9fa;
            min-height: 100vh;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .navbar {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
            box-shadow: 0 2px 20px rgba(255, 215, 0, 0.3);
            border-bottom: 2px solid #FFD700;
        }

        .sidebar {
            background: linear-gradient(180deg, #000000 0%, #1a1a1a 50%, #000000 100%);
            min-height: calc(100vh - 56px);
            box-shadow: 2px 0 20px rgba(255, 215, 0, 0.2);
            border-right: 2px solid #FFD700;
        }
        
        .sidebar .nav-link {
            color: #f8f9fa;
            padding: 12px 20px;
            border-radius: 12px;
            margin: 5px 10px;
            transition: all 0.4s ease;
            border: 1px solid transparent;
        }

        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #000000;
            transform: translateX(-8px);
            border: 1px solid #FFD700;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
            font-weight: 600;
        }

        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #000000;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.5);
        }
        
        .card {
            border: 2px solid #FFD700;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
            transition: all 0.4s ease;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #f8f9fa;
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 35px rgba(255, 215, 0, 0.4);
            border-color: #FFA500;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #000000;
            border: 2px solid #FFD700;
            font-weight: 600;
        }

        .stat-card.success {
            background: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
            color: #ffffff;
            border: 2px solid #32CD32;
        }

        .stat-card.warning {
            background: linear-gradient(135deg, #FF8C00 0%, #FFD700 100%);
            color: #000000;
            border: 2px solid #FF8C00;
        }

        .stat-card.danger {
            background: linear-gradient(135deg, #DC143C 0%, #FF6347 100%);
            color: #ffffff;
            border: 2px solid #DC143C;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border: 2px solid #FFD700;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 700;
            color: #000000;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
            color: #000000;
        }
        
        .table {
            border-radius: 20px;
            overflow: hidden;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #f8f9fa;
            border: 2px solid #FFD700;
        }

        .table thead th {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #000000;
            border: none;
            font-weight: 700;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .table tbody tr {
            background: rgba(26, 26, 26, 0.8);
            border-bottom: 1px solid rgba(255, 215, 0, 0.2);
        }

        .table tbody tr:hover {
            background: rgba(255, 215, 0, 0.1);
            transform: scale(1.02);
            transition: all 0.3s ease;
        }
        
        .alert {
            border: 2px solid #FFD700;
            border-radius: 15px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #f8f9fa;
        }

        .card-header {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #000000;
            font-weight: 700;
            border-bottom: 2px solid #FFD700;
            border-radius: 18px 18px 0 0 !important;
        }

        .card-title {
            color: #000000;
            font-weight: 700;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.3);
        }
        
        /* لوغو CSS بديل */
        .css-logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 45px;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(255,215,0,0.4);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .css-logo:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 6px 20px rgba(255,215,0,0.6);
        }

        .css-logo::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
            animation: logoShine 3s ease-in-out infinite;
        }

        @keyframes logoShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .crown {
            font-size: 12px;
            line-height: 1;
            margin-bottom: -2px;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
        }

        .letters-text {
            font-family: 'Times New Roman', serif;
            font-weight: 900;
            font-size: 16px;
            color: #000000;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.5);
            line-height: 1;
            direction: ltr !important;
            unicode-bidi: bidi-override;
            letter-spacing: 1px;
            transform: perspective(100px) rotateX(10deg);
        }

        .brand-text {
            font-weight: 700;
            font-size: 1.3rem;
            color: #FFD700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-right: 10px;
        }

        /* العلامة المائية */
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: -1;
            opacity: 0.05;
            pointer-events: none;
            user-select: none;
        }

        .watermark-logo {
            width: 400px;
            height: 400px;
            animation: watermarkFloat 20s ease-in-out infinite;
        }

        @keyframes watermarkFloat {
            0%, 100% {
                transform: translate(-50%, -50%) rotate(0deg) scale(1);
                opacity: 0.05;
            }
            25% {
                transform: translate(-50%, -50%) rotate(2deg) scale(1.02);
                opacity: 0.08;
            }
            50% {
                transform: translate(-50%, -50%) rotate(0deg) scale(1.05);
                opacity: 0.1;
            }
            75% {
                transform: translate(-50%, -50%) rotate(-2deg) scale(1.02);
                opacity: 0.08;
            }
        }

        /* WhatsApp Global Styles */
        .whatsapp-icon {
            color: #25D366 !important;
        }

        .btn-whatsapp {
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%) !important;
            border: 1px solid #25D366 !important;
            color: white !important;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(37, 211, 102, 0.3);
        }

        .btn-whatsapp:hover {
            background: linear-gradient(135deg, #128C7E 0%, #075E54 100%) !important;
            border-color: #128C7E !important;
            color: white !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
        }

        .btn-whatsapp:focus {
            box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25) !important;
            color: white !important;
        }

        .btn-whatsapp:active {
            background: linear-gradient(135deg, #075E54 0%, #128C7E 100%) !important;
            border-color: #075E54 !important;
            color: white !important;
        }

        /* WhatsApp Dropdown Styles */
        .dropdown-menu .dropdown-item:hover {
            background-color: rgba(37, 211, 102, 0.1);
        }

        .nav-link.dropdown-toggle:hover .whatsapp-icon {
            color: #128C7E !important;
        }

        /* WhatsApp Animation */
        @keyframes whatsapp-pulse {
            0% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(37, 211, 102, 0); }
            100% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0); }
        }

        .btn-whatsapp:hover {
            animation: whatsapp-pulse 1.5s infinite;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'dashboard' %}">
                <!-- لوغو CSS بديل -->
                <div class="css-logo me-2">
                    <div class="crown">👑</div>
                    <div class="letters-text">AB</div>
                </div>
                <span class="brand-text">{{ company_settings.company_name|default:"محلات أبو علاء" }}</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                {{ user.first_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'change_password' %}">
                                    <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'logout' %}">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            {% if user.is_authenticated %}
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'dashboard' %}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'customer_list' %}">
                                <i class="fas fa-users me-2"></i>
                                العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'debt_list' %}">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                الديون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'payment_list' %}">
                                <i class="fas fa-credit-card me-2"></i>
                                الدفعات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'invoice_list' %}">
                                <i class="fas fa-file-invoice me-2"></i>
                                الفواتير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'reports_dashboard' %}">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>

                        <hr class="sidebar-divider">

                        <!-- WhatsApp Section -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="whatsappDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fab fa-whatsapp me-2" style="color: #25D366;"></i>
                                واتساب
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'whatsapp_send_group' %}">
                                    <i class="fas fa-users me-2"></i>رسالة جماعية
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'whatsapp_send_overdue' %}">
                                    <i class="fas fa-exclamation-triangle me-2"></i>للمتأخرين
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'whatsapp_templates' %}">
                                    <i class="fas fa-templates me-2"></i>القوالب
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'whatsapp_history' %}">
                                    <i class="fas fa-history me-2"></i>تاريخ الرسائل
                                </a></li>
                            </ul>
                        </li>

                        <hr class="sidebar-divider">

                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'currency_settings' %}">
                                <i class="fas fa-coins me-2"></i>
                                إعدادات العملة
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'logo_settings' %}">
                                <i class="fas fa-image me-2"></i>
                                إعدادات الشعار
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'change_password' %}">
                                <i class="fas fa-key me-2"></i>
                                تغيير كلمة المرور
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'backup_dashboard' %}">
                                <i class="fas fa-cloud-upload-alt me-2"></i>
                                النسخ الاحتياطي
                            </a>
                        </li>
                        {% if user.is_superuser %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'admin:index' %}">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>
            {% endif %}

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- الإشعارات ستظهر عبر JavaScript -->
                <div id="page-messages" class="mt-3" style="display: none;">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                </div>

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- العلامة المائية -->
    {% load static %}
    <div class="watermark">
        <img src="{% static 'images/abu_alaa_logo.svg' %}"
             alt="Abu Alaa Watermark"
             class="watermark-logo">
    </div>

    <!-- Debug: عرض حالة الشعار -->
    {% if user.is_superuser %}
        <div style="position: fixed; bottom: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 5px; font-size: 12px; z-index: 10000;">
            Debug:
            {% if company_settings %}
                Settings: ✓ | Logo: {% if company_settings.logo %}✓ ({{ company_settings.logo.url }}){% else %}✗{% endif %}
            {% else %}
                Settings: ✗
            {% endif %}
        </div>
    {% endif %}

    <!-- حقوق المبرمج -->
    <div class="developer-footer">
        <i class="fas fa-code"></i>
        تطوير: المبرمج خالد شجاع © 2025
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- WhatsApp Utils -->
    <script src="{% load static %}{% static 'js/whatsapp.js' %}"></script>

    <!-- نظام الإشعارات المحسن -->
    <script>
        // إنشاء container للإشعارات إذا لم يكن موجود
        function createNotificationContainer() {
            let container = document.getElementById('notification-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'notification-container';
                container.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    max-width: 400px;
                `;
                document.body.appendChild(container);
            }
            return container;
        }

        // إظهار إشعار
        function showNotification(message, type = 'success', duration = 5000) {
            const container = createNotificationContainer();

            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show mb-2`;
            notification.style.cssText = `
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border: none;
                border-radius: 10px;
                animation: slideInRight 0.3s ease-out;
            `;

            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            container.appendChild(notification);

            // إزالة تلقائية بعد المدة المحددة
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 150);
                }
            }, duration);
        }

        // CSS للأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);

        // تتبع الإشعارات المعروضة لتجنب التكرار
        const shownNotifications = new Set();

        // حفظ الإشعار في localStorage (بدون إظهار تلقائي)
        function saveNotification(message, type) {
            const notifications = JSON.parse(localStorage.getItem('notifications') || '[]');
            const notificationId = `${message}_${type}_${Date.now()}`;

            // تجنب حفظ نفس الإشعار مرتين
            const exists = notifications.some(notif =>
                notif.message === message && notif.type === type &&
                (Date.now() - notif.timestamp) < 5000 // خلال 5 ثوان
            );

            if (!exists) {
                notifications.push({
                    id: notificationId,
                    message: message,
                    type: type,
                    timestamp: Date.now()
                });
                // الاحتفاظ بآخر 5 إشعارات فقط
                if (notifications.length > 5) {
                    notifications.shift();
                }
                localStorage.setItem('notifications', JSON.stringify(notifications));
            }
        }

        // مسح الإشعارات القديمة
        function clearOldNotifications() {
            const notifications = JSON.parse(localStorage.getItem('notifications') || '[]');
            const now = Date.now();
            // مسح الإشعارات الأقدم من 10 ثوان
            const recentNotifications = notifications.filter(notif => now - notif.timestamp < 10000);
            localStorage.setItem('notifications', JSON.stringify(recentNotifications));
        }

        // إظهار الإشعارات الموجودة من Django
        document.addEventListener('DOMContentLoaded', function() {
            // مسح الإشعارات القديمة أولاً
            clearOldNotifications();

            // مسح الإشعارات المعروضة من الجلسة السابقة
            shownNotifications.clear();

            {% if messages %}
                {% for message in messages %}
                    const messageText = '{{ message|escapejs }}';
                    const messageType = '{{ message.tags }}';
                    const messageId = `${messageText}_${messageType}`;

                    // تجنب إظهار نفس الإشعار مرتين
                    if (!shownNotifications.has(messageId)) {
                        showNotification(messageText, messageType);
                        shownNotifications.add(messageId);
                        saveNotification(messageText, messageType);
                    }
                {% endfor %}
            {% endif %}
        });

        // دالة عامة لإظهار إشعارات النجاح
        window.showSuccess = function(message) {
            const messageId = `${message}_success`;
            if (!shownNotifications.has(messageId)) {
                showNotification(message, 'success');
                shownNotifications.add(messageId);
                saveNotification(message, 'success');
            }
        };

        // دالة عامة لإظهار إشعارات الخطأ
        window.showError = function(message) {
            const messageId = `${message}_danger`;
            if (!shownNotifications.has(messageId)) {
                showNotification(message, 'danger');
                shownNotifications.add(messageId);
                saveNotification(message, 'danger');
            }
        };

        // دالة عامة لإظهار إشعارات التحذير
        window.showWarning = function(message) {
            const messageId = `${message}_warning`;
            if (!shownNotifications.has(messageId)) {
                showNotification(message, 'warning');
                shownNotifications.add(messageId);
                saveNotification(message, 'warning');
            }
        };

        // دالة عامة لإظهار إشعارات المعلومات
        window.showInfo = function(message) {
            const messageId = `${message}_info`;
            if (!shownNotifications.has(messageId)) {
                showNotification(message, 'info');
                shownNotifications.add(messageId);
                saveNotification(message, 'info');
            }
        };

        // دالة لإظهار إشعار عند رفع الملفات
        window.showUploadSuccess = function(filename) {
            const message = `تم رفع الملف "${filename}" بنجاح!`;
            showSuccess(message);
        };

        // دالة لإظهار إشعار عند حفظ البيانات
        window.showSaveSuccess = function(itemName) {
            const message = `تم حفظ ${itemName} بنجاح!`;
            showSuccess(message);
        };

        // مسح الإشعارات المعروضة عند تغيير الصفحة
        window.addEventListener('beforeunload', function() {
            shownNotifications.clear();
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
