{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}روابط واتساب للمتأخرين - محلات أبو علاء{% endblock %}

{% block extra_css %}
<style>
    .whatsapp-btn {
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border: none;
        color: white;
        padding: 8px 20px;
        border-radius: 20px;
        font-weight: bold;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(37, 211, 102, 0.3);
        font-size: 14px;
    }
    
    .whatsapp-btn:hover {
        background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
        color: white;
    }
    
    .whatsapp-btn-large {
        padding: 12px 25px;
        font-size: 16px;
        border-radius: 25px;
    }
    
    .customer-card {
        border: 2px solid #e9ecef;
        border-radius: 15px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }
    
    .customer-card.overdue {
        border-color: #dc3545;
        background: linear-gradient(135deg, #ffffff 0%, #fff5f5 100%);
    }
    
    .customer-card.due-soon {
        border-color: #ffc107;
        background: linear-gradient(135deg, #ffffff 0%, #fffbf0 100%);
    }
    
    .customer-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stats-card {
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        border: 2px solid;
        color: white;
    }
    
    .stats-card.overdue {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border-color: #dc3545;
    }
    
    .stats-card.due-soon {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: #212529;
        border-color: #ffc107;
    }
    
    .message-preview {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        border: 2px solid #dee2e6;
        margin-bottom: 30px;
    }
    
    .priority-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 10px;
        padding: 4px 8px;
        border-radius: 10px;
    }
    
    .amount-highlight {
        font-size: 18px;
        font-weight: bold;
    }
    
    .copy-btn {
        background: #6c757d;
        border: none;
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        transition: all 0.3s ease;
    }
    
    .copy-btn:hover {
        background: #5a6268;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        {% if target_type == 'overdue' %}
            <i class="fas fa-exclamation-triangle text-danger me-2"></i>
            روابط واتساب للمتأخرين
        {% else %}
            <i class="fas fa-clock text-warning me-2"></i>
            روابط واتساب للمستحقين قريباً
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'whatsapp_send_overdue' %}" class="btn btn-outline-primary">
                <i class="fas fa-plus me-1"></i>إرسال رسالة جديدة
            </a>
            <a href="{% url 'whatsapp_history' %}" class="btn btn-outline-info">
                <i class="fas fa-history me-1"></i>تاريخ الرسائل
            </a>
        </div>
        <a href="{% url 'debt_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للديون
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card {% if target_type == 'overdue' %}overdue{% else %}due-soon{% endif %}">
            <h3 class="mb-1">{{ whatsapp_links|length }}</h3>
            <p class="mb-0">
                {% if target_type == 'overdue' %}
                    عميل متأخر
                {% else %}
                    عميل مستحق قريباً
                {% endif %}
            </p>
        </div>
    </div>
    <div class="col-md-8">
        <div class="message-preview">
            <h6 class="mb-3">
                <i class="fas fa-eye me-2"></i>معاينة الرسالة المرسلة
            </h6>
            <div class="border rounded p-3 bg-white">
                {{ message_content|linebreaks }}
            </div>
            <div class="mt-2">
                <button class="copy-btn" onclick="copyMessage()">
                    <i class="fas fa-copy me-1"></i>نسخ النص
                </button>
            </div>
        </div>
    </div>
</div>

<!-- قائمة العملاء والروابط -->
<div class="row">
    {% for link in whatsapp_links %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="customer-card {% if target_type == 'overdue' %}overdue{% else %}due-soon{% endif %} p-3 position-relative">
            <!-- شارة الأولوية -->
            {% if target_type == 'overdue' %}
                <span class="priority-badge bg-danger">متأخر</span>
            {% else %}
                <span class="priority-badge bg-warning text-dark">قريب</span>
            {% endif %}
            
            <div class="mb-3">
                <h6 class="mb-1">{{ link.customer.name }}</h6>
                <small class="text-muted">
                    <i class="fas fa-phone me-1"></i>
                    {{ link.customer.phone }}
                </small>
            </div>
            
            <!-- معلومات المبالغ -->
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="small text-muted">إجمالي المتبقي:</span>
                    <span class="badge bg-info">{{ link.customer.remaining_debt|currency }}</span>
                </div>
                {% if target_type == 'overdue' %}
                <div class="d-flex justify-content-between align-items-center">
                    <span class="small text-muted">المبلغ المتأخر:</span>
                    <span class="amount-highlight text-danger">{{ link.amount|currency }}</span>
                </div>
                {% else %}
                <div class="d-flex justify-content-between align-items-center">
                    <span class="small text-muted">المستحق قريباً:</span>
                    <span class="amount-highlight text-warning">{{ link.amount|currency }}</span>
                </div>
                {% endif %}
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="d-grid gap-2">
                <a href="{{ link.url }}" target="_blank" class="whatsapp-btn whatsapp-btn-large">
                    <i class="fab fa-whatsapp"></i>
                    فتح واتساب
                </a>
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-secondary btn-sm" onclick="copyLink('{{ link.url }}', this)">
                        <i class="fas fa-link me-1"></i>نسخ الرابط
                    </button>
                    <a href="{% url 'customer_detail' link.customer.id %}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-user me-1"></i>التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- أزرار إضافية -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <div class="btn-group" role="group">
            <button class="btn btn-outline-success" onclick="openAllLinks()">
                <i class="fab fa-whatsapp me-1"></i>
                فتح جميع الروابط
            </button>
            <button class="btn btn-outline-info" onclick="copyAllLinks()">
                <i class="fas fa-copy me-1"></i>
                نسخ جميع الروابط
            </button>
            <button class="btn btn-outline-primary" onclick="exportToText()">
                <i class="fas fa-download me-1"></i>
                تصدير كملف نصي
            </button>
            <button class="btn btn-outline-warning" onclick="exportToExcel()">
                <i class="fas fa-file-excel me-1"></i>
                تصدير Excel
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>إحصائيات المبالغ
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h5 class="text-primary">{{ whatsapp_links|length }}</h5>
                        <small class="text-muted">عدد العملاء</small>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-info">
                            {% widthratio whatsapp_links|length 1 1 as total_amount %}
                            {{ total_amount|floatformat:0 }}
                        </h5>
                        <small class="text-muted">إجمالي المبالغ</small>
                    </div>
                    <div class="col-md-3">
                        <h5 class="{% if target_type == 'overdue' %}text-danger{% else %}text-warning{% endif %}">
                            {% for link in whatsapp_links %}
                                {% if forloop.first %}{{ link.amount|currency }}{% endif %}
                            {% endfor %}
                        </h5>
                        <small class="text-muted">متوسط المبلغ</small>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-success">{{ whatsapp_links|length }}</h5>
                        <small class="text-muted">روابط جاهزة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast للإشعارات -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="copyToast" class="toast" role="alert">
        <div class="toast-header">
            <i class="fas fa-check-circle text-success me-2"></i>
            <strong class="me-auto">تم النسخ</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            تم نسخ الرابط بنجاح!
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// نسخ محتوى الرسالة
function copyMessage() {
    const messageText = `{{ message_content|escapejs }}`;
    navigator.clipboard.writeText(messageText).then(() => {
        showToast('تم نسخ نص الرسالة!');
    });
}

// نسخ رابط واحد
function copyLink(url, button) {
    navigator.clipboard.writeText(url).then(() => {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check me-1"></i>تم النسخ';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
        
        showToast('تم نسخ الرابط!');
    });
}

// فتح جميع الروابط
function openAllLinks() {
    const links = [
        {% for link in whatsapp_links %}
        '{{ link.url }}',
        {% endfor %}
    ];
    
    if (confirm(`هل تريد فتح ${links.length} رابط واتساب للعملاء {% if target_type == 'overdue' %}المتأخرين{% else %}المستحقين قريباً{% endif %}؟`)) {
        links.forEach((link, index) => {
            setTimeout(() => {
                window.open(link, '_blank');
            }, index * 1500); // تأخير 1.5 ثانية بين كل رابط
        });
    }
}

// نسخ جميع الروابط
function copyAllLinks() {
    const links = [
        {% for link in whatsapp_links %}
        '{{ link.customer.name }} ({{ link.amount|currency }}): {{ link.url }}',
        {% endfor %}
    ];
    
    const allLinksText = links.join('\n');
    navigator.clipboard.writeText(allLinksText).then(() => {
        showToast(`تم نسخ ${links.length} رابط!`);
    });
}

// تصدير كملف نصي
function exportToText() {
    const content = [
        'روابط واتساب للعملاء {% if target_type == "overdue" %}المتأخرين{% else %}المستحقين قريباً{% endif %} - {{ "now"|date:"Y-m-d H:i" }}',
        '=' .repeat(70),
        '',
        'محتوى الرسالة:',
        '{{ message_content|escapejs }}',
        '',
        'قائمة العملاء والروابط:',
        '=' .repeat(30),
        {% for link in whatsapp_links %}
        '{{ link.customer.name }} - {{ link.customer.phone }}',
        'المبلغ: {{ link.amount|currency }}',
        'الرابط: {{ link.url }}',
        '-' .repeat(50),
        {% endfor %}
    ].join('\n');
    
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'whatsapp_{% if target_type == "overdue" %}overdue{% else %}due_soon{% endif %}_{{ "now"|date:"Y-m-d_H-i" }}.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// تصدير Excel (محاكاة)
function exportToExcel() {
    const csvContent = [
        'الاسم,الهاتف,المبلغ,الرابط',
        {% for link in whatsapp_links %}
        '"{{ link.customer.name }}","{{ link.customer.phone }}","{{ link.amount }}","{{ link.url }}"',
        {% endfor %}
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'whatsapp_{% if target_type == "overdue" %}overdue{% else %}due_soon{% endif %}_{{ "now"|date:"Y-m-d_H-i" }}.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// عرض Toast
function showToast(message) {
    const toastElement = document.getElementById('copyToast');
    const toastBody = toastElement.querySelector('.toast-body');
    toastBody.textContent = message;
    
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
}
</script>
{% endblock %}
