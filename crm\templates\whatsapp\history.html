{% extends 'base.html' %}

{% block title %}تاريخ رسائل واتساب - محلات أبو علاء{% endblock %}

{% block extra_css %}
<style>
    .whatsapp-btn {
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: bold;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    }
    
    .whatsapp-btn:hover {
        background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        color: white;
    }
    
    .message-card {
        border: 2px solid #e9ecef;
        border-radius: 15px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        margin-bottom: 20px;
    }
    
    .message-card:hover {
        border-color: #25D366;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.2);
        transform: translateY(-2px);
    }
    
    .message-type-badge {
        font-size: 12px;
        padding: 4px 12px;
        border-radius: 15px;
    }
    
    .type-individual { background-color: #17a2b8; color: white; }
    .type-group { background-color: #28a745; color: white; }
    .type-overdue { background-color: #dc3545; color: white; }
    
    .status-badge {
        font-size: 11px;
        padding: 3px 10px;
        border-radius: 12px;
    }
    
    .status-draft { background-color: #6c757d; color: white; }
    .status-sent { background-color: #28a745; color: white; }
    .status-failed { background-color: #dc3545; color: white; }
    
    .message-content {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        border: 1px solid #dee2e6;
        max-height: 150px;
        overflow-y: auto;
    }
    
    .recipients-list {
        max-height: 100px;
        overflow-y: auto;
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 10px;
    }
    
    .filter-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        border: 2px solid #dee2e6;
        margin-bottom: 30px;
    }
    
    .stats-row {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
        border: 1px solid #bbdefb;
    }
    
    .stat-item {
        text-align: center;
        padding: 15px;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.7);
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-history me-2 text-info"></i>
        تاريخ رسائل واتساب
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'whatsapp_send_individual' 1 %}" class="btn btn-outline-primary">
                <i class="fas fa-user me-1"></i>رسالة فردية
            </a>
            <a href="{% url 'whatsapp_send_group' %}" class="btn btn-outline-success">
                <i class="fas fa-users me-1"></i>رسالة جماعية
            </a>
            <a href="{% url 'whatsapp_send_overdue' %}" class="btn btn-outline-warning">
                <i class="fas fa-exclamation-triangle me-1"></i>للمتأخرين
            </a>
        </div>
        <a href="{% url 'whatsapp_templates' %}" class="btn btn-outline-info">
            <i class="fas fa-templates me-1"></i>القوالب
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="stats-row">
    <div class="row">
        <div class="col-md-3">
            <div class="stat-item">
                <h4 class="text-primary mb-1">{{ messages.paginator.count }}</h4>
                <small class="text-muted">إجمالي الرسائل</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-item">
                <h4 class="text-success mb-1">
                    {{ messages.object_list|length }}
                </h4>
                <small class="text-muted">رسائل مرسلة</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-item">
                <h4 class="text-info mb-1">
                    {% for message in messages %}
                        {% if message.message_type == 'group' %}{{ message.recipients_count|add:0 }}{% endif %}
                    {% endfor %}
                </h4>
                <small class="text-muted">رسائل جماعية</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-item">
                <h4 class="text-warning mb-1">
                    {% for message in messages %}
                        {% if message.message_type == 'overdue' %}{{ message.recipients_count|add:0 }}{% endif %}
                    {% endfor %}
                </h4>
                <small class="text-muted">رسائل متأخرين</small>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="filter-card">
    <h6 class="mb-3">
        <i class="fas fa-filter me-2"></i>تصفية الرسائل
    </h6>
    
    <form method="get" class="row g-3">
        <div class="col-md-4">
            <label for="messageType" class="form-label">نوع الرسالة</label>
            <select class="form-select" id="messageType" name="type">
                <option value="">جميع الأنواع</option>
                {% for value, label in message_types %}
                <option value="{{ value }}" {% if current_type == value %}selected{% endif %}>
                    {{ label }}
                </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-4">
            <label for="dateFrom" class="form-label">من تاريخ</label>
            <input type="date" class="form-control" id="dateFrom" name="date_from">
        </div>
        <div class="col-md-4">
            <label for="dateTo" class="form-label">إلى تاريخ</label>
            <input type="date" class="form-control" id="dateTo" name="date_to">
        </div>
        <div class="col-12">
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>تصفية
                </button>
                <a href="{% url 'whatsapp_history' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>إلغاء التصفية
                </a>
            </div>
        </div>
    </form>
</div>

<!-- قائمة الرسائل -->
<div class="row">
    {% for message in messages %}
    <div class="col-12 mb-4">
        <div class="message-card p-4">
            <div class="row">
                <div class="col-md-8">
                    <!-- معلومات الرسالة -->
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <span class="message-type-badge type-{{ message.message_type }}">
                                {{ message.get_message_type_display }}
                            </span>
                            <span class="status-badge status-{{ message.status }} ms-2">
                                {{ message.get_status_display }}
                            </span>
                        </div>
                        <div class="text-end">
                            <small class="text-muted d-block">
                                <i class="fas fa-calendar me-1"></i>
                                {{ message.created_at|date:"Y-m-d H:i" }}
                            </small>
                            {% if message.sent_at %}
                            <small class="text-muted d-block">
                                <i class="fas fa-paper-plane me-1"></i>
                                {{ message.sent_at|date:"Y-m-d H:i" }}
                            </small>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- محتوى الرسالة -->
                    <div class="mb-3">
                        <label class="form-label small text-muted">محتوى الرسالة:</label>
                        <div class="message-content">
                            {{ message.message_content|linebreaks }}
                        </div>
                    </div>
                    
                    <!-- معلومات إضافية -->
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                أنشئ بواسطة: {{ message.created_by.first_name|default:message.created_by.username }}
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>
                                عدد المستقبلين: {{ message.recipients_count }}
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- قائمة المستقبلين -->
                    <div class="mb-3">
                        <label class="form-label small text-muted">المستقبلين:</label>
                        <div class="recipients-list">
                            {% for recipient in message.recipients.all|slice:":5" %}
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small>{{ recipient.name }}</small>
                                <small class="text-muted">{{ recipient.phone }}</small>
                            </div>
                            {% endfor %}
                            {% if message.recipients_count > 5 %}
                            <small class="text-muted">
                                و {{ message.recipients_count|add:"-5" }} آخرين...
                            </small>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="viewDetails({{ message.id }})">
                            <i class="fas fa-eye me-1"></i>عرض التفاصيل
                        </button>
                        {% if message.message_type != 'individual' %}
                        <button class="btn btn-outline-success btn-sm" onclick="resendMessage({{ message.id }})">
                            <i class="fas fa-redo me-1"></i>إعادة الإرسال
                        </button>
                        {% endif %}
                        <button class="btn btn-outline-info btn-sm" onclick="copyMessage({{ message.id }})">
                            <i class="fas fa-copy me-1"></i>نسخ المحتوى
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد رسائل</h5>
            <p class="text-muted">لم يتم إرسال أي رسائل واتساب بعد</p>
            <a href="{% url 'whatsapp_send_group' %}" class="whatsapp-btn">
                <i class="fab fa-whatsapp"></i>
                إرسال أول رسالة
            </a>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if messages.has_other_pages %}
<nav aria-label="تنقل الصفحات">
    <ul class="pagination justify-content-center">
        {% if messages.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ messages.previous_page_number }}{% if current_type %}&type={{ current_type }}{% endif %}">السابق</a>
            </li>
        {% endif %}
        
        {% for num in messages.paginator.page_range %}
            {% if messages.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
            {% else %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}{% if current_type %}&type={{ current_type }}{% endif %}">{{ num }}</a>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if messages.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ messages.next_page_number }}{% if current_type %}&type={{ current_type }}{% endif %}">التالي</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- Modal تفاصيل الرسالة -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>تفاصيل الرسالة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalContent">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// عرض تفاصيل الرسالة
function viewDetails(messageId) {
    // يمكن إضافة AJAX call لجلب التفاصيل
    document.getElementById('modalContent').innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('detailsModal')).show();
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
        document.getElementById('modalContent').innerHTML = `
            <div class="alert alert-info">
                <strong>معرف الرسالة:</strong> ${messageId}<br>
                <strong>الحالة:</strong> مرسلة<br>
                <strong>عدد المستقبلين:</strong> 5 عملاء
            </div>
        `;
    }, 1000);
}

// إعادة إرسال الرسالة
function resendMessage(messageId) {
    if (confirm('هل تريد إعادة إرسال هذه الرسالة؟')) {
        // يمكن إضافة منطق إعادة الإرسال هنا
        alert('تم إعادة إرسال الرسالة بنجاح!');
    }
}

// نسخ محتوى الرسالة
function copyMessage(messageId) {
    // يمكن إضافة منطق نسخ المحتوى هنا
    navigator.clipboard.writeText('محتوى الرسالة').then(() => {
        alert('تم نسخ محتوى الرسالة!');
    });
}
</script>
{% endblock %}
