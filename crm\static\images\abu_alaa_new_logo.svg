<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 400" width="300" height="400">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700"/>
      <stop offset="50%" style="stop-color:#FFA500"/>
      <stop offset="100%" style="stop-color:#FF8C00"/>
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- الخلفية الداكنة -->
  <rect width="300" height="400" fill="#1a1a1a" rx="20"/>
  
  <!-- الشكل الرئيسي - قطرة مع حرف A -->
  <g transform="translate(150, 120)">
    <!-- القطرة الخارجية -->
    <path d="M0,-80 C-40,-80 -60,-40 -60,0 C-60,40 -40,80 0,80 C40,80 60,40 60,0 C60,-40 40,-80 0,-80 Z" 
          fill="url(#goldGradient)" 
          stroke="#FFD700" 
          stroke-width="3" 
          filter="url(#glow)"/>
    
    <!-- القطرة الداخلية -->
    <path d="M0,-60 C-25,-60 -40,-30 -40,0 C-40,25 -25,50 0,50 C25,50 40,25 40,0 C40,-30 25,-60 0,-60 Z" 
          fill="none" 
          stroke="#FFD700" 
          stroke-width="2"/>
    
    <!-- حرف A في المنتصف -->
    <path d="M-15,20 L0,-20 L15,20 M-10,5 L10,5" 
          stroke="#1a1a1a" 
          stroke-width="4" 
          stroke-linecap="round" 
          stroke-linejoin="round"/>
  </g>
  
  <!-- النص العربي -->
  <text x="150" y="280" 
        font-family="Arial, sans-serif" 
        font-size="36" 
        font-weight="bold" 
        text-anchor="middle" 
        fill="url(#goldGradient)" 
        filter="url(#glow)">أبو علاء</text>
  
  <!-- النص الإنجليزي -->
  <text x="150" y="320" 
        font-family="Times New Roman, serif" 
        font-size="24" 
        font-weight="bold" 
        text-anchor="middle" 
        fill="url(#goldGradient)" 
        letter-spacing="2px">ABU ALAA</text>
</svg>
