server {
    listen 80;
    server_name localhost;  # غير هذا إلى اسم النطاق الخاص بك أو IP

    # إعدادات الأمان
    client_max_body_size 100M;

    # إعداد الملفات الثابتة (Windows paths)
    location /static/ {
        alias Z:/crm/staticfiles/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # إعداد ملفات الوسائط (Windows paths)
    location /media/ {
        alias Z:/crm/media/;
        expires 7d;
        add_header Cache-Control "public";
    }

    # إعداد التطبيق الرئيسي
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # إعدادات المهلة الزمنية
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # إعدادات WebSocket (إذا كنت تستخدمها)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # إعدادات الضغط
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}

# إعادة توجيه HTTPS (اختياري)
# server {
#     listen 443 ssl;
#     server_name localhost;
#     
#     ssl_certificate /path/to/certificate.crt;
#     ssl_certificate_key /path/to/private.key;
#     
#     # باقي الإعدادات نفسها كما في الأعلى
# }
