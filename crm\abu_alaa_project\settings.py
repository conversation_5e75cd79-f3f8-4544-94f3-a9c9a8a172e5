"""
Django settings for abu_alaa_project project.

Generated by 'django-admin startproject' using Django 5.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

import os
from pathlib import Path
import environ
import dj_database_url

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Environment variables
env = environ.Env(
    DEBUG=(bool, False)
)

# Read .env file if it exists
environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY', default='django-insecure-#mehl_u=(lm^k3rbp0lc(55ma$o=9!7!c2j#t-5+%yg0i2fxbj')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env('DEBUG', default=True)

ALLOWED_HOSTS = env.list('ALLOWED_HOSTS', default=['*'])  # السماح للوصول من أي عنوان IP

# إعدادات CSRF للعمل مع Cloudflare Tunnel
CSRF_TRUSTED_ORIGINS = [
    'https://*.trycloudflare.com',
    'https://chargers-gallery-west-reporters.trycloudflare.com',
    'https://*.ngrok.io',
    'https://*.ngrok-free.app',
    'https://*.onrender.com',
    'https://abualaa.onrender.com',
    'http://localhost:8080',
    'http://127.0.0.1:8080',
]

# السماح لجميع النطاقات المؤقتة
CSRF_COOKIE_SECURE = False  # للتطوير فقط
CSRF_COOKIE_SAMESITE = 'Lax'


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'crm',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'abu_alaa_project.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'crm.context_processors.company_settings',
                'crm.context_processors.developer_info',
            ],
        },
    },
]

WSGI_APPLICATION = 'abu_alaa_project.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': env.db_url(
        'DATABASE_URL',
        default='sqlite:///' + str(BASE_DIR / 'db.sqlite3')
    )
}

# إعدادات قاعدة البيانات للنشر السحابي
if 'DATABASE_URL' in os.environ:
    DATABASES['default'] = dj_database_url.parse(os.environ['DATABASE_URL'])


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'ar'

TIME_ZONE = 'Asia/Riyadh'

USE_I18N = True

USE_TZ = True

# RTL Support
USE_L10N = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'

STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# إعدادات WhiteNoise للملفات الثابتة
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# إعدادات الملفات المرفوعة
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# إعدادات Google Drive API
GOOGLE_DRIVE_CREDENTIALS = env('GOOGLE_DRIVE_CREDENTIALS', default=None)
GOOGLE_DRIVE_FOLDER_ID = env('GOOGLE_DRIVE_FOLDER_ID', default=None)

# إعدادات Railway
import os
if 'RAILWAY_ENVIRONMENT' in os.environ:
    DEBUG = False
    ALLOWED_HOSTS = ['*']

    # إعدادات الأمان للإنتاج
    SECURE_SSL_REDIRECT = False  # Railway يتعامل مع SSL
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    USE_X_FORWARDED_HOST = True

# إعدادات Render
if 'RENDER' in os.environ:
    DEBUG = False
    ALLOWED_HOSTS = ['*']

    # إعدادات الأمان للإنتاج
    SECURE_SSL_REDIRECT = False  # Render يتعامل مع SSL
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    USE_X_FORWARDED_HOST = True

    # تعطيل CSRF للاختبار
    CSRF_COOKIE_SECURE = False
    CSRF_USE_SESSIONS = False

    # استخدام PostgreSQL من Render
    import dj_database_url
    DATABASES = {
        'default': dj_database_url.parse(
            env('DATABASE_URL', default='sqlite:///db.sqlite3'),
            conn_max_age=600,
            conn_health_checks=True,
        )
    }
    USE_X_FORWARDED_PORT = True

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Login URLs
LOGIN_URL = '/login/'
LOGIN_REDIRECT_URL = '/dashboard/'
LOGOUT_REDIRECT_URL = '/login/'

# Company Settings
COMPANY_NAME = 'محلات أبو علاء'
COMPANY_PHONE = '+964 XX XXX XXXX'
COMPANY_EMAIL = '<EMAIL>'
COMPANY_ADDRESS = 'العراق'
DEFAULT_CURRENCY = 'دولار أمريكي'

# إعدادات النسخ الاحتياطي
BACKUP_FOLDER = BASE_DIR / 'backups'

# إعدادات Google Drive (يجب إعدادها للاستخدام)
# GOOGLE_DRIVE_CREDENTIALS = BASE_DIR / 'credentials.json'
# GOOGLE_DRIVE_FOLDER_ID = 'your_google_drive_folder_id'

# إعدادات إضافية للأمان مع Tunnels
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
USE_X_FORWARDED_HOST = True
USE_X_FORWARDED_PORT = True

# إعدادات Session للعمل مع النطاقات المختلفة
SESSION_COOKIE_SECURE = False  # للتطوير
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_HTTPONLY = True

# إعدادات إضافية للتوافق
SECURE_SSL_REDIRECT = False  # للتطوير
X_FRAME_OPTIONS = 'SAMEORIGIN'
