{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}إرسال رسالة واتساب جماعية - محلات أبو علاء{% endblock %}

{% block extra_css %}
<style>
    .whatsapp-btn {
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: bold;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    }
    
    .whatsapp-btn:hover {
        background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        color: white;
    }
    
    .customer-checkbox {
        transform: scale(1.2);
        margin-right: 10px;
    }
    
    .customer-row {
        transition: all 0.3s ease;
        border-radius: 8px;
        padding: 8px;
    }
    
    .customer-row:hover {
        background-color: rgba(37, 211, 102, 0.1);
    }
    
    .customer-row.selected {
        background-color: rgba(37, 211, 102, 0.2);
        border: 1px solid #25D366;
    }
    
    .template-card {
        border: 2px solid #e9ecef;
        border-radius: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .template-card:hover {
        border-color: #25D366;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.2);
    }
    
    .template-card.selected {
        border-color: #25D366;
        background-color: rgba(37, 211, 102, 0.1);
    }
    
    .selection-summary {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        border: 2px solid #dee2e6;
        position: sticky;
        top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fab fa-whatsapp me-2 text-success"></i>
        إرسال رسالة واتساب جماعية
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'customer_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة لقائمة العملاء
        </a>
    </div>
</div>

<form method="post" id="groupWhatsappForm">
    {% csrf_token %}
    
    <div class="row">
        <!-- اختيار العملاء -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>اختيار العملاء
                    </h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="selectAll">تحديد الكل</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="clearAll">إلغاء التحديد</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <input type="text" class="form-control" id="customerSearch" placeholder="البحث في العملاء...">
                    </div>
                    
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-hover">
                            <thead class="sticky-top bg-light">
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="masterCheckbox" class="customer-checkbox">
                                    </th>
                                    <th>الاسم</th>
                                    <th>الهاتف</th>
                                    <th>المبلغ المتبقي</th>
                                </tr>
                            </thead>
                            <tbody id="customersTable">
                                {% for customer in customers %}
                                <tr class="customer-row" data-customer-name="{{ customer.name|lower }}">
                                    <td>
                                        <input type="checkbox" name="customer_ids" value="{{ customer.id }}" 
                                               class="customer-checkbox customer-check">
                                    </td>
                                    <td><strong>{{ customer.name }}</strong></td>
                                    <td>
                                        <i class="fas fa-phone me-1"></i>
                                        {{ customer.phone }}
                                    </td>
                                    <td>
                                        {% if customer.remaining_debt > 0 %}
                                            <span class="badge bg-warning">{{ customer.remaining_debt|currency }}</span>
                                        {% else %}
                                            <span class="badge bg-success">مسدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- ملخص التحديد ونموذج الرسالة -->
        <div class="col-md-4">
            <div class="selection-summary mb-4">
                <h6 class="mb-3">
                    <i class="fas fa-check-circle me-2"></i>ملخص التحديد
                </h6>
                <div class="d-flex justify-content-between mb-2">
                    <span>العملاء المحددين:</span>
                    <span class="badge bg-primary" id="selectedCount">0</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>إجمالي العملاء:</span>
                    <span class="badge bg-info">{{ customers.count }}</span>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fab fa-whatsapp me-2"></i>محتوى الرسالة
                    </h6>
                </div>
                <div class="card-body">
                    <!-- اختيار القالب -->
                    {% if templates %}
                    <div class="mb-3">
                        <label class="form-label">قالب جاهز</label>
                        <div class="row">
                            {% for template in templates %}
                            <div class="col-12 mb-2">
                                <div class="template-card p-2" data-template-id="{{ template.id }}" data-content="{{ template.content }}">
                                    <small><strong>{{ template.name }}</strong></small>
                                    <br>
                                    <small class="text-muted">{{ template.content|truncatewords:5 }}</small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="template_id" id="templateId">
                    </div>
                    {% endif %}
                    
                    <!-- محتوى الرسالة -->
                    <div class="mb-3">
                        <label for="messageContent" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="messageContent" name="message_content" rows="6" 
                                  placeholder="اكتب رسالتك هنا..." required></textarea>
                        <div class="form-text small">
                            <strong>متغيرات:</strong> <code>{customer_name}</code>, <code>{company_name}</code>, <code>{remaining_debt}</code>
                        </div>
                    </div>
                    
                    <!-- زر الإرسال -->
                    <div class="d-grid">
                        <button type="submit" class="whatsapp-btn" id="sendButton" disabled>
                            <i class="fab fa-whatsapp"></i>
                            إنشاء روابط واتساب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const customerChecks = document.querySelectorAll('.customer-check');
    const masterCheckbox = document.getElementById('masterCheckbox');
    const selectedCount = document.getElementById('selectedCount');
    const sendButton = document.getElementById('sendButton');
    const customerSearch = document.getElementById('customerSearch');
    const customersTable = document.getElementById('customersTable');
    const templateCards = document.querySelectorAll('.template-card');
    const templateIdInput = document.getElementById('templateId');
    const messageContent = document.getElementById('messageContent');
    
    // تحديث عداد التحديد
    function updateSelectedCount() {
        const selected = document.querySelectorAll('.customer-check:checked').length;
        selectedCount.textContent = selected;
        sendButton.disabled = selected === 0;
        
        // تحديث حالة الـ master checkbox
        if (selected === 0) {
            masterCheckbox.indeterminate = false;
            masterCheckbox.checked = false;
        } else if (selected === customerChecks.length) {
            masterCheckbox.indeterminate = false;
            masterCheckbox.checked = true;
        } else {
            masterCheckbox.indeterminate = true;
        }
    }
    
    // معالجة تحديد العملاء
    customerChecks.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const row = this.closest('.customer-row');
            if (this.checked) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
            updateSelectedCount();
        });
    });
    
    // Master checkbox
    masterCheckbox.addEventListener('change', function() {
        customerChecks.forEach(checkbox => {
            checkbox.checked = this.checked;
            const row = checkbox.closest('.customer-row');
            if (this.checked) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        });
        updateSelectedCount();
    });
    
    // أزرار تحديد الكل وإلغاء التحديد
    document.getElementById('selectAll').addEventListener('click', function() {
        masterCheckbox.checked = true;
        masterCheckbox.dispatchEvent(new Event('change'));
    });
    
    document.getElementById('clearAll').addEventListener('click', function() {
        masterCheckbox.checked = false;
        masterCheckbox.dispatchEvent(new Event('change'));
    });
    
    // البحث في العملاء
    customerSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = customersTable.querySelectorAll('.customer-row');
        
        rows.forEach(row => {
            const customerName = row.dataset.customerName;
            if (customerName.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
    
    // اختيار القالب
    templateCards.forEach(card => {
        card.addEventListener('click', function() {
            templateCards.forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            
            templateIdInput.value = this.dataset.templateId;
            messageContent.value = this.dataset.content;
        });
    });
    
    // تحديث العداد الأولي
    updateSelectedCount();
});
</script>
{% endblock %}
