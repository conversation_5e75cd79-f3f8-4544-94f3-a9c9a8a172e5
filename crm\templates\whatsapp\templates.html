{% extends 'base.html' %}

{% block title %}قوالب رسائل واتساب - محلات أبو علاء{% endblock %}

{% block extra_css %}
<style>
    .template-card {
        border: 2px solid #e9ecef;
        border-radius: 15px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }
    
    .template-card:hover {
        border-color: #25D366;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.2);
        transform: translateY(-2px);
    }
    
    .template-card.active {
        border-color: #25D366;
        background: linear-gradient(135deg, #ffffff 0%, rgba(37, 211, 102, 0.1) 100%);
    }
    
    .template-card.inactive {
        border-color: #dc3545;
        background: linear-gradient(135deg, #ffffff 0%, rgba(220, 53, 69, 0.1) 100%);
    }
    
    .whatsapp-btn {
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: bold;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    }
    
    .whatsapp-btn:hover {
        background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        color: white;
    }
    
    .type-badge {
        font-size: 12px;
        padding: 4px 12px;
        border-radius: 15px;
    }
    
    .type-payment_reminder { background-color: #ffc107; color: #212529; }
    .type-overdue_notice { background-color: #dc3545; color: white; }
    .type-payment_received { background-color: #28a745; color: white; }
    .type-general { background-color: #6c757d; color: white; }
    
    .variables-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 10px;
        padding: 15px;
        border: 1px solid #bbdefb;
        margin-bottom: 20px;
    }
    
    .create-form {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 25px;
        border: 2px solid #dee2e6;
        margin-bottom: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-templates me-2 text-primary"></i>
        قوالب رسائل واتساب
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'whatsapp_send_group' %}" class="btn btn-outline-success">
                <i class="fab fa-whatsapp me-1"></i>إرسال رسالة
            </a>
            <a href="{% url 'whatsapp_history' %}" class="btn btn-outline-info">
                <i class="fas fa-history me-1"></i>تاريخ الرسائل
            </a>
        </div>
        <a href="{% url 'dashboard' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- معلومات المتغيرات -->
<div class="variables-info">
    <h6 class="mb-3">
        <i class="fas fa-info-circle me-2"></i>المتغيرات المتاحة في القوالب
    </h6>
    <div class="row">
        <div class="col-md-6">
            <ul class="list-unstyled mb-0">
                <li><code>{customer_name}</code> - اسم العميل</li>
                <li><code>{company_name}</code> - اسم الشركة</li>
                <li><code>{phone}</code> - هاتف الشركة</li>
            </ul>
        </div>
        <div class="col-md-6">
            <ul class="list-unstyled mb-0">
                <li><code>{remaining_debt}</code> - إجمالي المبلغ المتبقي</li>
                <li><code>{overdue_amount}</code> - المبلغ المتأخر</li>
            </ul>
        </div>
    </div>
</div>

<!-- نموذج إنشاء قالب جديد -->
<div class="create-form">
    <h5 class="mb-4">
        <i class="fas fa-plus-circle me-2"></i>إنشاء قالب جديد
    </h5>
    
    <form method="post">
        {% csrf_token %}
        <input type="hidden" name="action" value="create">
        
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="templateName" class="form-label">اسم القالب</label>
                <input type="text" class="form-control" id="templateName" name="name" required>
            </div>
            <div class="col-md-6 mb-3">
                <label for="templateType" class="form-label">نوع القالب</label>
                <select class="form-select" id="templateType" name="template_type" required>
                    {% for value, label in template_types %}
                    <option value="{{ value }}">{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        
        <div class="mb-3">
            <label for="templateContent" class="form-label">محتوى القالب</label>
            <textarea class="form-control" id="templateContent" name="content" rows="6" 
                      placeholder="اكتب محتوى القالب هنا... يمكنك استخدام المتغيرات المذكورة أعلاه" required></textarea>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <button type="submit" class="whatsapp-btn">
                <i class="fas fa-save"></i>
                حفظ القالب
            </button>
        </div>
    </form>
</div>

<!-- قائمة القوالب الموجودة -->
<div class="row">
    {% for template in templates %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="template-card {% if template.is_active %}active{% else %}inactive{% endif %} p-3">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <div>
                    <h6 class="mb-1">{{ template.name }}</h6>
                    <span class="type-badge type-{{ template.template_type }}">
                        {{ template.get_template_type_display }}
                    </span>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" 
                           {% if template.is_active %}checked{% endif %}
                           onchange="toggleTemplate({{ template.id }}, this)">
                    <label class="form-check-label small">
                        {% if template.is_active %}نشط{% else %}معطل{% endif %}
                    </label>
                </div>
            </div>
            
            <div class="mb-3">
                <small class="text-muted">المحتوى:</small>
                <div class="border rounded p-2 bg-light small" style="max-height: 100px; overflow-y: auto;">
                    {{ template.content|linebreaks }}
                </div>
            </div>
            
            <div class="mb-3">
                <small class="text-muted d-block">تاريخ الإنشاء: {{ template.created_at|date:"Y-m-d" }}</small>
                {% if template.updated_at != template.created_at %}
                <small class="text-muted d-block">آخر تحديث: {{ template.updated_at|date:"Y-m-d" }}</small>
                {% endif %}
            </div>
            
            <div class="d-grid gap-2">
                <button class="btn btn-outline-primary btn-sm" onclick="previewTemplate({{ template.id }})">
                    <i class="fas fa-eye me-1"></i>معاينة
                </button>
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-secondary btn-sm" onclick="editTemplate({{ template.id }})">
                        <i class="fas fa-edit me-1"></i>تعديل
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteTemplate({{ template.id }})">
                        <i class="fas fa-trash me-1"></i>حذف
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-templates fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد قوالب محفوظة</h5>
            <p class="text-muted">ابدأ بإنشاء قالب جديد باستخدام النموذج أعلاه</p>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Modal معاينة القالب -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>معاينة القالب
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label"><strong>اسم القالب:</strong></label>
                    <p id="previewName"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>النوع:</strong></label>
                    <p id="previewType"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>المحتوى الأصلي:</strong></label>
                    <div class="border rounded p-3 bg-light" id="previewOriginal"></div>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>المحتوى بعد استبدال المتغيرات:</strong></label>
                    <div class="border rounded p-3 bg-success text-white" id="previewProcessed"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="whatsapp-btn" onclick="useTemplate()">
                    <i class="fab fa-whatsapp"></i>استخدام القالب
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تفعيل/إلغاء تفعيل القالب
function toggleTemplate(templateId, checkbox) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        {% csrf_token %}
        <input type="hidden" name="action" value="toggle">
        <input type="hidden" name="template_id" value="${templateId}">
    `;
    
    document.body.appendChild(form);
    form.submit();
}

// معاينة القالب
function previewTemplate(templateId) {
    // هنا يمكن إضافة AJAX call لجلب بيانات القالب
    // للتبسيط، سنستخدم بيانات وهمية
    
    document.getElementById('previewName').textContent = 'قالب تذكير بالدفع';
    document.getElementById('previewType').textContent = 'تذكير بالدفع';
    document.getElementById('previewOriginal').innerHTML = 'مرحباً {customer_name}<br>نذكركم بوجود مبلغ {remaining_debt} مستحق للدفع.<br>شكراً لكم<br>{company_name}';
    document.getElementById('previewProcessed').innerHTML = 'مرحباً أحمد محمد<br>نذكركم بوجود مبلغ 500,000 د.ع مستحق للدفع.<br>شكراً لكم<br>محلات أبو علاء';
    
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// تعديل القالب
function editTemplate(templateId) {
    // يمكن إضافة modal للتعديل أو الانتقال لصفحة تعديل
    alert('ميزة التعديل ستكون متاحة قريباً');
}

// حذف القالب
function deleteTemplate(templateId) {
    if (confirm('هل أنت متأكد من حذف هذا القالب؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            {% csrf_token %}
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="template_id" value="${templateId}">
        `;
        
        document.body.appendChild(form);
        form.submit();
    }
}

// استخدام القالب
function useTemplate() {
    window.location.href = '{% url "whatsapp_send_group" %}';
}

// معاينة المحتوى أثناء الكتابة
document.getElementById('templateContent').addEventListener('input', function() {
    // يمكن إضافة معاينة مباشرة هنا
});
</script>
{% endblock %}
