{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}إرسال رسالة واتساب - {{ customer.name }} - محلات أبو علاء{% endblock %}

{% block extra_css %}
<style>
    .whatsapp-btn {
        background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: bold;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    }
    
    .whatsapp-btn:hover {
        background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        color: white;
    }
    
    .whatsapp-icon {
        font-size: 18px;
    }
    
    .template-card {
        border: 2px solid #e9ecef;
        border-radius: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .template-card:hover {
        border-color: #25D366;
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.2);
    }
    
    .template-card.selected {
        border-color: #25D366;
        background-color: rgba(37, 211, 102, 0.1);
    }
    
    .customer-info {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        border: 2px solid #dee2e6;
    }
    
    .debt-badge {
        font-size: 14px;
        padding: 8px 15px;
        border-radius: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fab fa-whatsapp me-2 text-success"></i>
        إرسال رسالة واتساب - {{ customer.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'customer_detail' customer.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة لتفاصيل العميل
        </a>
    </div>
</div>

<div class="row">
    <!-- معلومات العميل -->
    <div class="col-md-4 mb-4">
        <div class="customer-info">
            <h5 class="mb-3">
                <i class="fas fa-user me-2"></i>معلومات العميل
            </h5>
            
            <div class="mb-3">
                <label class="form-label text-muted">الاسم</label>
                <p class="mb-0"><strong>{{ customer.name }}</strong></p>
            </div>
            
            <div class="mb-3">
                <label class="form-label text-muted">رقم الهاتف</label>
                <p class="mb-0">
                    <i class="fas fa-phone me-1"></i>
                    {{ customer.phone }}
                </p>
            </div>
            
            <div class="mb-3">
                <label class="form-label text-muted">إجمالي الديون</label>
                <p class="mb-0">
                    <span class="debt-badge bg-info">{{ customer.total_debt|currency }}</span>
                </p>
            </div>
            
            <div class="mb-3">
                <label class="form-label text-muted">المبلغ المتبقي</label>
                <p class="mb-0">
                    {% if customer.remaining_debt > 0 %}
                        <span class="debt-badge bg-warning">{{ customer.remaining_debt|currency }}</span>
                    {% else %}
                        <span class="debt-badge bg-success">مسدد</span>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
    
    <!-- نموذج الرسالة -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fab fa-whatsapp me-2"></i>إنشاء رسالة واتساب
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="whatsappForm">
                    {% csrf_token %}
                    
                    <!-- اختيار القالب -->
                    {% if templates %}
                    <div class="mb-4">
                        <label class="form-label">اختيار قالب جاهز (اختياري)</label>
                        <div class="row">
                            {% for template in templates %}
                            <div class="col-md-6 mb-3">
                                <div class="template-card p-3" data-template-id="{{ template.id }}" data-content="{{ template.content }}">
                                    <h6 class="mb-2">{{ template.name }}</h6>
                                    <small class="text-muted">{{ template.get_template_type_display }}</small>
                                    <p class="mb-0 mt-2 small">{{ template.content|truncatewords:10 }}</p>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="template_id" id="templateId">
                    </div>
                    {% endif %}
                    
                    <!-- محتوى الرسالة -->
                    <div class="mb-4">
                        <label for="messageContent" class="form-label">محتوى الرسالة</label>
                        <textarea class="form-control" id="messageContent" name="message_content" rows="6" 
                                  placeholder="اكتب رسالتك هنا...&#10;&#10;يمكنك استخدام المتغيرات التالية:&#10;{customer_name} - اسم العميل&#10;{company_name} - اسم الشركة&#10;{remaining_debt} - المبلغ المتبقي&#10;{phone} - هاتف الشركة" required></textarea>
                        <div class="form-text">
                            <strong>المتغيرات المتاحة:</strong>
                            <code>{customer_name}</code>, <code>{company_name}</code>, <code>{remaining_debt}</code>, <code>{phone}</code>
                        </div>
                    </div>
                    
                    <!-- معاينة الرسالة -->
                    <div class="mb-4">
                        <label class="form-label">معاينة الرسالة</label>
                        <div class="border rounded p-3 bg-light" id="messagePreview" style="min-height: 100px;">
                            <em class="text-muted">ستظهر معاينة الرسالة هنا...</em>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="whatsapp-btn">
                            <i class="fab fa-whatsapp whatsapp-icon"></i>
                            إنشاء رابط واتساب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض رابط WhatsApp -->
<div class="modal fade" id="whatsappModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fab fa-whatsapp me-2 text-success"></i>
                    رابط واتساب جاهز
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <p class="mb-3">تم إنشاء رابط واتساب بنجاح!</p>
                <a href="#" id="whatsappLink" class="whatsapp-btn" target="_blank">
                    <i class="fab fa-whatsapp whatsapp-icon"></i>
                    فتح واتساب الآن
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const templateCards = document.querySelectorAll('.template-card');
    const templateIdInput = document.getElementById('templateId');
    const messageContent = document.getElementById('messageContent');
    const messagePreview = document.getElementById('messagePreview');
    const form = document.getElementById('whatsappForm');
    
    // اختيار القالب
    templateCards.forEach(card => {
        card.addEventListener('click', function() {
            // إزالة التحديد من جميع البطاقات
            templateCards.forEach(c => c.classList.remove('selected'));
            
            // تحديد البطاقة الحالية
            this.classList.add('selected');
            
            // تعيين القيم
            templateIdInput.value = this.dataset.templateId;
            messageContent.value = this.dataset.content;
            
            // تحديث المعاينة
            updatePreview();
        });
    });
    
    // تحديث المعاينة عند الكتابة
    messageContent.addEventListener('input', updatePreview);
    
    function updatePreview() {
        let content = messageContent.value;
        if (content) {
            // استبدال المتغيرات للمعاينة
            content = content
                .replace(/{customer_name}/g, '{{ customer.name }}')
                .replace(/{company_name}/g, '{{ company_settings.company_name|default:"محلات أبو علاء" }}')
                .replace(/{remaining_debt}/g, '{{ customer.remaining_debt|currency }}')
                .replace(/{phone}/g, '{{ company_settings.phone|default:"" }}');
            
            messagePreview.innerHTML = content.replace(/\n/g, '<br>');
        } else {
            messagePreview.innerHTML = '<em class="text-muted">ستظهر معاينة الرسالة هنا...</em>';
        }
    }
    
    // معالجة إرسال النموذج
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('whatsappLink').href = data.whatsapp_url;
                new bootstrap.Modal(document.getElementById('whatsappModal')).show();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إنشاء الرابط');
        });
    });
    
    // تحديث المعاينة الأولية
    updatePreview();
});
</script>
{% endblock %}
